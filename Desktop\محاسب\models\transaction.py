from .database import db
from datetime import datetime

class Transaction:
    def __init__(self, id=None, client_id=None, service_id=None, amount=0.0, 
                 transaction_type="due", transaction_date=None, notes=""):
        self.id = id
        self.client_id = client_id
        self.service_id = service_id
        self.amount = amount
        self.transaction_type = transaction_type  # 'payment' or 'due'
        self.transaction_date = transaction_date or datetime.now()
        self.notes = notes
    
    @classmethod
    def create(cls, client_id, amount, transaction_type, service_id=None, notes=""):
        """Create a new transaction"""
        query = """
            INSERT INTO transactions (client_id, service_id, amount, transaction_type, notes)
            VALUES (?, ?, ?, ?, ?)
        """
        transaction_id = db.execute_query(query, (client_id, service_id, amount, transaction_type, notes))
        return cls.get_by_id(transaction_id)
    
    @classmethod
    def get_all(cls):
        """Get all transactions"""
        query = """
            SELECT t.*, c.name as client_name, s.name as service_name
            FROM transactions t
            LEFT JOIN clients c ON t.client_id = c.id
            LEFT JOIN services s ON t.service_id = s.id
            ORDER BY t.transaction_date DESC
        """
        rows = db.execute_query(query)
        return [cls.from_row_with_details(row) for row in rows]
    
    @classmethod
    def get_by_id(cls, transaction_id):
        """Get transaction by ID"""
        query = "SELECT * FROM transactions WHERE id = ?"
        rows = db.execute_query(query, (transaction_id,))
        if rows:
            return cls.from_row(rows[0])
        return None
    
    @classmethod
    def get_by_client(cls, client_id):
        """Get all transactions for a specific client"""
        query = """
            SELECT t.*, s.name as service_name
            FROM transactions t
            LEFT JOIN services s ON t.service_id = s.id
            WHERE t.client_id = ?
            ORDER BY t.transaction_date DESC
        """
        rows = db.execute_query(query, (client_id,))
        return [cls.from_row_with_service(row) for row in rows]
    
    def update(self):
        """Update transaction"""
        query = """
            UPDATE transactions 
            SET client_id = ?, service_id = ?, amount = ?, 
                transaction_type = ?, notes = ?
            WHERE id = ?
        """
        db.execute_query(query, (self.client_id, self.service_id, self.amount, 
                                self.transaction_type, self.notes, self.id))
    
    def delete(self):
        """Delete transaction"""
        query = "DELETE FROM transactions WHERE id = ?"
        db.execute_query(query, (self.id,))
    
    @classmethod
    def get_total_revenue(cls):
        """Get total revenue (all payments)"""
        query = """
            SELECT COALESCE(SUM(amount), 0) as total
            FROM transactions 
            WHERE transaction_type = 'payment'
        """
        rows = db.execute_query(query)
        return rows[0]['total'] if rows else 0
    
    @classmethod
    def get_total_outstanding(cls):
        """Get total outstanding balance"""
        query = """
            SELECT 
                COALESCE(SUM(CASE WHEN transaction_type = 'due' THEN amount ELSE 0 END), 0) -
                COALESCE(SUM(CASE WHEN transaction_type = 'payment' THEN amount ELSE 0 END), 0) as outstanding
            FROM transactions
        """
        rows = db.execute_query(query)
        return rows[0]['outstanding'] if rows else 0
    
    @classmethod
    def from_row(cls, row):
        """Create Transaction object from database row"""
        return cls(
            id=row['id'],
            client_id=row['client_id'],
            service_id=row['service_id'],
            amount=row['amount'],
            transaction_type=row['transaction_type'],
            transaction_date=row['transaction_date'],
            notes=row['notes']
        )
    
    @classmethod
    def from_row_with_details(cls, row):
        """Create Transaction object from database row with client and service names"""
        transaction = cls.from_row(row)
        transaction.client_name = row.get('client_name', '')
        transaction.service_name = row.get('service_name', '')
        return transaction
    
    @classmethod
    def from_row_with_service(cls, row):
        """Create Transaction object from database row with service name"""
        transaction = cls.from_row(row)
        transaction.service_name = row.get('service_name', '')
        return transaction
    
    def __str__(self):
        return f"Transaction({self.transaction_type}, {self.amount}, {self.transaction_date})"
