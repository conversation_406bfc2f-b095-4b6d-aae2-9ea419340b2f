from .database import db
from datetime import datetime

class Client:
    def __init__(self, id=None, name="", phone="", email="", notes="", created_date=None):
        self.id = id
        self.name = name
        self.phone = phone
        self.email = email
        self.notes = notes
        self.created_date = created_date or datetime.now()
    
    @classmethod
    def create(cls, name, phone="", email="", notes=""):
        """Create a new client"""
        query = """
            INSERT INTO clients (name, phone, email, notes)
            VALUES (?, ?, ?, ?)
        """
        client_id = db.execute_query(query, (name, phone, email, notes))
        return cls.get_by_id(client_id)
    
    @classmethod
    def get_all(cls):
        """Get all clients"""
        query = "SELECT * FROM clients ORDER BY name"
        rows = db.execute_query(query)
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def get_by_id(cls, client_id):
        """Get client by ID"""
        query = "SELECT * FROM clients WHERE id = ?"
        rows = db.execute_query(query, (client_id,))
        if rows:
            return cls.from_row(rows[0])
        return None
    
    @classmethod
    def search(cls, search_term):
        """Search clients by name or phone"""
        query = """
            SELECT * FROM clients 
            WHERE name LIKE ? OR phone LIKE ?
            ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        rows = db.execute_query(query, (search_pattern, search_pattern))
        return [cls.from_row(row) for row in rows]
    
    def update(self):
        """Update client information"""
        query = """
            UPDATE clients 
            SET name = ?, phone = ?, email = ?, notes = ?
            WHERE id = ?
        """
        db.execute_query(query, (self.name, self.phone, self.email, self.notes, self.id))
    
    def delete(self):
        """Delete client"""
        query = "DELETE FROM clients WHERE id = ?"
        db.execute_query(query, (self.id,))
    
    def get_balance(self):
        """Calculate client's current balance"""
        query = """
            SELECT 
                COALESCE(SUM(CASE WHEN transaction_type = 'payment' THEN amount ELSE 0 END), 0) as total_paid,
                COALESCE(SUM(CASE WHEN transaction_type = 'due' THEN amount ELSE 0 END), 0) as total_due
            FROM transactions 
            WHERE client_id = ?
        """
        rows = db.execute_query(query, (self.id,))
        if rows:
            row = rows[0]
            total_paid = row['total_paid']
            total_due = row['total_due']
            balance = total_due - total_paid
            return {
                'total_paid': total_paid,
                'total_due': total_due,
                'balance': balance
            }
        return {'total_paid': 0, 'total_due': 0, 'balance': 0}
    
    def get_transactions(self):
        """Get all transactions for this client"""
        from .transaction import Transaction
        return Transaction.get_by_client(self.id)
    
    @classmethod
    def from_row(cls, row):
        """Create Client object from database row"""
        return cls(
            id=row['id'],
            name=row['name'],
            phone=row['phone'],
            email=row['email'],
            notes=row['notes'],
            created_date=row['created_date']
        )
    
    def __str__(self):
        return f"Client({self.name}, {self.phone})"
