from .database import db
from datetime import datetime

class Service:
    def __init__(self, id=None, name="", price=0.0, created_date=None):
        self.id = id
        self.name = name
        self.price = price
        self.created_date = created_date or datetime.now()
    
    @classmethod
    def create(cls, name, price):
        """Create a new service"""
        query = """
            INSERT INTO services (name, price)
            VALUES (?, ?)
        """
        service_id = db.execute_query(query, (name, price))
        return cls.get_by_id(service_id)
    
    @classmethod
    def get_all(cls):
        """Get all services"""
        query = "SELECT * FROM services ORDER BY name"
        rows = db.execute_query(query)
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def get_by_id(cls, service_id):
        """Get service by ID"""
        query = "SELECT * FROM services WHERE id = ?"
        rows = db.execute_query(query, (service_id,))
        if rows:
            return cls.from_row(rows[0])
        return None
    
    def update(self):
        """Update service information"""
        query = """
            UPDATE services 
            SET name = ?, price = ?
            WHERE id = ?
        """
        db.execute_query(query, (self.name, self.price, self.id))
    
    def delete(self):
        """Delete service"""
        query = "DELETE FROM services WHERE id = ?"
        db.execute_query(query, (self.id,))
    
    @classmethod
    def from_row(cls, row):
        """Create Service object from database row"""
        return cls(
            id=row['id'],
            name=row['name'],
            price=row['price'],
            created_date=row['created_date']
        )
    
    def __str__(self):
        return f"Service({self.name}, {self.price})"
