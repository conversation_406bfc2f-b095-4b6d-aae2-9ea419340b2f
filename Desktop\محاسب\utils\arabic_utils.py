def reshape_arabic_text(text):
    """Reshape Arabic text for proper display (simplified version)"""
    if not text:
        return ""

    # For now, return text as-is
    # In a full implementation, you would use arabic_reshaper and bidi
    return text

def format_currency(amount):
    """Format currency with Arabic-friendly formatting"""
    try:
        return f"{amount:,.2f} ريال"
    except:
        return f"{amount} ريال"

def format_date_arabic(date_obj):
    """Format date in Arabic-friendly format"""
    try:
        if isinstance(date_obj, str):
            from datetime import datetime
            date_obj = datetime.fromisoformat(date_obj.replace('Z', '+00:00'))

        return date_obj.strftime("%Y/%m/%d %H:%M")
    except:
        return str(date_obj)
